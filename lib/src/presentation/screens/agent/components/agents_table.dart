import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import '../../../../core/config/json_consts.dart';
import '../../dashboard/components/rounderd_icon_btn.dart';
import 'dart:math' as math;
import '../../../../core/theme/app_theme.dart';
import '../../../../core/theme/app_fonts.dart';
import '../../../../core/config/constants.dart';
import '../../../../core/config/responsive.dart';
import '../../../../core/config/app_strings.dart';
import '../../../../domain/models/agent.dart';
import '../../dashboard/components/brokers_table.dart';
import '../../dashboard/components/dashboard_content.dart';

class AgentsTable extends HookWidget {
  const AgentsTable({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final currentPage = useState(1);
    final itemsPerPage = useState(10);
    final searchQuery = useState('');
    final showFilter = useState(false);
    final selectedAgent = useState<String?>(null);
    final selectedLevel = useState<String?>(null);
    final selectedStatus = useState<String?>(null);
    final showTooltip = useState(false);

    // Applied filters - these are used for tooltip and actual filtering
    final appliedAgent = useState<String?>(null);
    final appliedLevel = useState<String?>(null);
    final appliedStatus = useState<String?>(null);

    //TODO: need to integrat API
    List<Agent> getFilteredAgents() {
      List<Agent> filtered = agentListJson;

      if (searchQuery.value.isNotEmpty) {
        filtered = filtered
            .where(
              (agent) => agent.name.toLowerCase().contains(
                searchQuery.value.toLowerCase(),
              ),
            )
            .toList();
      }

      // Apply filters using the applied filter values (set when Apply button is clicked)
      if (appliedAgent.value != null && appliedAgent.value!.isNotEmpty) {
        // Filter by agent logic here
      }

      if (appliedLevel.value != null && appliedLevel.value!.isNotEmpty) {
        filtered = filtered
            .where((agent) => agent.level == appliedLevel.value)
            .toList();
      }

      if (appliedStatus.value != null && appliedStatus.value!.isNotEmpty) {
        filtered = filtered
            .where(
              (agent) =>
                  (appliedStatus.value == 'Active' && agent.status) ||
                  (appliedStatus.value == 'Inactive' && !agent.status),
            )
            .toList();
      }

      return filtered;
    }

    List<Agent> getPaginatedAgents() {
      final filtered = getFilteredAgents();
      final startIndex = (currentPage.value - 1) * itemsPerPage.value;
      final endIndex = (startIndex + itemsPerPage.value).clamp(
        0,
        filtered.length,
      );
      return filtered.sublist(startIndex, endIndex);
    }

    int getTotalPages() =>
        (getFilteredAgents().length / itemsPerPage.value).ceil();

    return LayoutBuilder(
      builder: (context, constraints) {
        return Column(
          children: [
            Container(
              width: constraints.maxWidth,
              padding: const EdgeInsets.only(bottom: defaultPadding - 2),
              decoration: BoxDecoration(
                color: AppTheme.white,
                borderRadius: BorderRadius.circular(15),
                boxShadow: [
                  BoxShadow(
                    color: AppTheme.black.withValues(alpha: 0.05),
                    blurRadius: 5,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(15),

                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    if (showFilter.value)
                      _buildFilterWidget(
                        context,
                        selectedAgent,
                        selectedLevel,
                        selectedStatus,
                        showFilter,
                        currentPage,
                        showTooltip,
                        appliedAgent,
                        appliedLevel,
                        appliedStatus,
                      ),
                    Padding(
                      padding: const EdgeInsets.fromLTRB(
                        defaultPadding * 1.5,
                        defaultPadding - 2,
                        defaultPadding * 1.5,
                        0,
                      ),
                      child: _buildHeader(
                        context,
                        searchQuery,
                        showFilter,
                        selectedAgent,
                        selectedLevel,
                        selectedStatus,
                        showTooltip,
                        appliedAgent,
                        appliedLevel,
                        appliedStatus,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Padding(
                      padding: const EdgeInsets.symmetric(
                        horizontal: defaultPadding * 1.5,
                      ),
                      child: _buildTable(
                        context,
                        constraints,
                        getPaginatedAgents(),
                      ),
                    ),
                    const SizedBox(height: defaultPadding),
                    Padding(
                      padding: const EdgeInsets.symmetric(
                        horizontal: defaultPadding * 1.5,
                      ),
                      child: _buildFooter(
                        context,
                        currentPage,
                        itemsPerPage,
                        getFilteredAgents(),
                        getTotalPages(),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            SizedBox(height: defaultPadding * 2),
            const Footer(),
          ],
        );
      },
    );
  }

  Widget _buildHeader(
    BuildContext context,
    ValueNotifier<String> searchQuery,
    ValueNotifier<bool> showFilter,
    ValueNotifier<String?> selectedAgent,
    ValueNotifier<String?> selectedLevel,
    ValueNotifier<String?> selectedStatus,
    ValueNotifier<bool> showTooltip,
    ValueNotifier<String?> appliedAgent,
    ValueNotifier<String?> appliedLevel,
    ValueNotifier<String?> appliedStatus,
  ) {
    // For very small mobile devices, use a column layout
    if (Responsive.isSmallMobile(context)) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              RoundIconBtn(
                icon: 'user',
                backgroundColor: Colors.transparent,
                onPressed: () {},
                iconSize: 20,
              ),
              const SizedBox(width: 5),
              Expanded(
                child: Text(agents, style: AppFonts.semiBoldTextStyle(18)),
              ),
              Padding(
                padding: const EdgeInsets.only(left: 16),
                child: _buildFilterResultsTooltip(
                  appliedAgent.value,
                  appliedLevel.value,
                  appliedStatus.value,
                  appliedAgent,
                  appliedLevel,
                  appliedStatus,
                  showTooltip,
                  selectedAgent,
                  selectedLevel,
                  selectedStatus,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              MouseRegion(
                cursor: SystemMouseCursors.click,
                child: GestureDetector(
                  onTap: () {
                    showFilter.value = !showFilter.value;
                    if (showFilter.value) {
                      // When opening filter, restore previously selected values
                      selectedAgent.value = appliedAgent.value;
                      selectedLevel.value = appliedLevel.value;
                      selectedStatus.value = appliedStatus.value;
                    }
                  },
                  child: Container(
                    width: ResponsiveSizes.filterButtonWidth(context),
                    height: 40,
                    decoration: BoxDecoration(
                      color: showFilter.value
                          ? AppTheme.selectedComboBoxBorder
                          : AppTheme.searchbarBg,
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SizedBox(
                          width: 16,
                          height: 16,
                          child: Image.asset(
                            '$iconAssetpath/filter.png',
                            color: showFilter.value
                                ? Colors.white
                                : AppTheme.tableDataFont,
                          ),
                        ),
                        const SizedBox(width: 6),
                        Text(
                          filter,
                          style: AppFonts.regularTextStyle(
                            14,
                            color: showFilter.value
                                ? Colors.white
                                : AppTheme.tableDataFont,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: SizedBox(
                  height: 40,
                  child: TextField(
                    onChanged: (value) => searchQuery.value = value,
                    decoration: InputDecoration(
                      hintText: searchAgent,
                      hintStyle: AppFonts.regularTextStyle(
                        14,
                        color: AppTheme.tableDataFont,
                      ),
                      prefixIcon: Container(
                        height: 24,
                        width: 24,
                        padding: const EdgeInsets.only(
                          left: 8,
                          top: 8,
                          bottom: 8,
                        ),
                        child: Image.asset('$iconAssetpath/search.png'),
                      ),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(20),
                        borderSide: BorderSide.none,
                      ),
                      filled: true,
                      fillColor: AppTheme.searchbarBg,
                      contentPadding: const EdgeInsets.symmetric(vertical: 0),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      );
    }

    // For larger screens, use the original row layout
    return Row(
      children: [
        Row(
          children: [
            SizedBox(
              width: 20,
              height: 20,
              child: Image.asset('$iconAssetpath/user.png'),
            ),
            const SizedBox(width: 8),
            Text(agents, style: AppFonts.semiBoldTextStyle(22)),
            Padding(
              padding: const EdgeInsets.only(left: 16),
              child: _buildFilterResultsTooltip(
                appliedAgent.value,
                appliedLevel.value,
                appliedStatus.value,
                appliedAgent,
                appliedLevel,
                appliedStatus,
                showTooltip,
                selectedAgent,
                selectedLevel,
                selectedStatus,
              ),
            ),
          ],
        ),
        const Spacer(),
        Row(
          children: [
            MouseRegion(
              cursor: SystemMouseCursors.click,
              child: GestureDetector(
                onTap: () => showFilter.value = !showFilter.value,
                child: Container(
                  width: ResponsiveSizes.filterButtonWidth(context),
                  height: 40,
                  decoration: BoxDecoration(
                    color: showFilter.value
                        ? AppTheme.selectedComboBoxBorder
                        : AppTheme.searchbarBg,
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      SizedBox(
                        width: 16,
                        height: 16,
                        child: Image.asset(
                          '$iconAssetpath/filter.png',
                          color: showFilter.value
                              ? Colors.white
                              : AppTheme.tableDataFont,
                        ),
                      ),
                      const SizedBox(width: 6),
                      Text(
                        filter,
                        style: AppFonts.regularTextStyle(
                          14,
                          color: showFilter.value
                              ? Colors.white
                              : AppTheme.tableDataFont,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),

            const SizedBox(width: 8),
            SizedBox(
              width: ResponsiveSizes.searchFieldWidth(context),
              height: 40,
              child: TextField(
                onChanged: (value) => searchQuery.value = value,
                decoration: InputDecoration(
                  hintText: searchAgent,
                  hintStyle: AppFonts.regularTextStyle(
                    14,
                    color: AppTheme.tableDataFont,
                  ),
                  prefixIcon: Container(
                    height: 24,
                    width: 24,
                    padding: const EdgeInsets.only(left: 8, top: 8, bottom: 8),
                    child: Image.asset('$iconAssetpath/search.png'),
                  ),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(20),
                    borderSide: BorderSide.none,
                  ),
                  filled: true,
                  fillColor: AppTheme.searchbarBg,
                  contentPadding: const EdgeInsets.symmetric(vertical: 0),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildFilterResultsTooltip(
    String? agent,
    String? level,
    String? status,
    ValueNotifier<String?> appliedAgent,
    ValueNotifier<String?> appliedLevel,
    ValueNotifier<String?> appliedStatus,
    ValueNotifier<bool> showTooltip,
    ValueNotifier<String?> selectedAgent,
    ValueNotifier<String?> selectedLevel,
    ValueNotifier<String?> selectedStatus,
  ) {
    List<String> filters = [];
    if (agent != null) filters.add("$agentFilter - $agent");
    if (level != null) filters.add("$agentLevel - $level");
    if (status != null) filters.add("$agentStatus - $status");

    // Only show tooltip if showTooltip is true and filters are not empty
    if (filters.isEmpty || !showTooltip.value) return const SizedBox.shrink();

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: AppTheme.applyFilterTooltip,
        borderRadius: BorderRadius.circular(20),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          RichText(
            text: TextSpan(
              style: AppFonts.regularTextStyle(12, color: Colors.black),
              children: [
                const TextSpan(text: filterResult),
                ...filters
                    .map(
                      (filter) => TextSpan(
                        text: filter,
                        style: AppFonts.boldTextStyle(12, color: Colors.black),
                      ),
                    )
                    .expand(
                      (span) => [
                        span,
                        if (filter != filters.last) const TextSpan(text: ", "),
                      ],
                    ),
              ],
            ),
          ),
          const SizedBox(width: 8),
          GestureDetector(
            onTap: () {
              appliedAgent.value = null;
              appliedLevel.value = null;
              appliedStatus.value = null;
              selectedAgent.value = null;
              selectedLevel.value = null;
              selectedStatus.value = null;
              // Hide tooltip when close button is clicked
              showTooltip.value = false;
            },
            child: const Icon(Icons.close, size: 16, color: Colors.black),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterWidget(
    BuildContext context,
    ValueNotifier<String?> selectedAgent,
    ValueNotifier<String?> selectedLevel,
    ValueNotifier<String?> selectedStatus,
    ValueNotifier<bool> showFilter,
    ValueNotifier<int> currentPage,
    ValueNotifier<bool> showTooltip,
    ValueNotifier<String?> appliedAgent,
    ValueNotifier<String?> appliedLevel,
    ValueNotifier<String?> appliedStatus,
  ) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(color: AppTheme.filterBgColor),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(filterBy, style: AppFonts.semiBoldTextStyle(18)),
              GestureDetector(
                onTap: () {
                  showFilter.value = false;
                },
                child: const Icon(Icons.close, size: 20),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              SizedBox(
                width: ResponsiveSizes.comboBoxWidth(context),
                child: _buildDropdown(
                  selectAgent,
                  selectedAgent.value,
                  ['Agent 1', 'Agent 2', 'Agent 3'],
                  (value) => selectedAgent.value = value,
                ),
              ),
              const SizedBox(width: 12),
              SizedBox(
                width: ResponsiveSizes.comboBoxWidth(context),
                child: _buildDropdown(
                  selectLevel,
                  selectedLevel.value,
                  ['Junior', 'Senior', 'Manager'],
                  (value) => selectedLevel.value = value,
                ),
              ),
              const SizedBox(width: 12),
              SizedBox(
                width: ResponsiveSizes.comboBoxWidth(context),
                child: _buildDropdown(
                  selectStatus,
                  selectedStatus.value,
                  ['Active', 'Inactive'],
                  (value) => selectedStatus.value = value,
                ),
              ),
              const SizedBox(width: 12),
              SizedBox(
                width: ResponsiveSizes.applyButtonWidth(context),
                height: 40,
                child: ElevatedButton(
                  onPressed: () {
                    // Copy selected values to applied values
                    appliedAgent.value = selectedAgent.value;
                    appliedLevel.value = selectedLevel.value;
                    appliedStatus.value = selectedStatus.value;

                    currentPage.value = 1;
                    // Show tooltip only when Apply button is clicked
                    showTooltip.value = true;
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppTheme.roundIconColor,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 24,
                      vertical: 12,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(20),
                    ),
                  ),
                  child: const Text(apply),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDropdown(
    String hint,
    String? value,
    List<String> items,
    Function(String?) onChanged,
  ) {
    return Container(
      height: 40,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          // Add conditional border color
          color: value != null
              ? AppTheme.selectedComboBoxBorder
              : AppTheme.comboBoxBorder,
          width: 1.0,
        ),
      ),
      child: DropdownButtonHideUnderline(
        child: ButtonTheme(
          alignedDropdown: true,
          child: DropdownButton<String>(
            value: value,
            hint: Padding(
              padding: const EdgeInsets.only(left: 12),
              child: Text(
                hint,
                style: AppFonts.regularTextStyle(14, color: AppTheme.black),
              ),
            ),
            isExpanded: true,
            dropdownColor: Colors.white,
            menuMaxHeight: 300,
            // Add these properties to control dropdown position and style
            borderRadius: BorderRadius.circular(20),
            // Force dropdown to appear below
            items: items.map((item) {
              return DropdownMenuItem<String>(
                value: item,
                child: Padding(
                  padding: const EdgeInsets.only(left: 12),
                  child: Text(item, style: AppFonts.regularTextStyle(14)),
                ),
              );
            }).toList(),
            onChanged: onChanged,
          ),
        ),
      ),
    );
  }

  Widget _buildTable(
    BuildContext context,
    BoxConstraints constraints,
    List<Agent> agents,
  ) {
    if (Responsive.isMobile(context)) {
      return _buildMobileTable(context, agents);
    } else if (Responsive.isTablet(context)) {
      return _buildTabletTable(context, constraints, agents);
    } else {
      return _buildDesktopTable(context, constraints, agents);
    }
  }

  Widget _buildDesktopTable(
    BuildContext context,
    BoxConstraints constraints,
    List<Agent> agents,
  ) {
    final ScrollController _horizontalScrollController = ScrollController();

    final bool enableScroll = constraints.maxWidth <= 1230;

    Widget tableContent = SizedBox(
      width: math.max(constraints.maxWidth - (defaultPadding * 2), 1200),
      child: DataTable(
        columnSpacing: defaultPadding * 0.8,
        dataRowMinHeight: 40,
        dataRowMaxHeight: 50,
        horizontalMargin: 0,
        checkboxHorizontalMargin: 0,
        columns: [
          _dataColumn(name: agentName),
          _dataColumn(name: agentContact),
          _dataColumn(name: agentEmail),
          _dataColumn(name: agentJoinDate),
          _dataColumn(name: agentState),
          _dataColumn(name: agentCity),
          _dataColumn(name: agentLevel),
          _dataColumn(name: agentTotalDeals),
          _dataColumn(name: agentEarning),
          _dataColumn(name: agentStatus),
          _dataColumn(name: actions, allowSort: false),
        ],
        rows: agents
            .map((agent) => _agentDesktopDataRow(context, agent))
            .toList(),
      ),
    );

    if (enableScroll) {
      return Scrollbar(
        controller: _horizontalScrollController,
        thumbVisibility: true,
        child: SingleChildScrollView(
          controller: _horizontalScrollController,
          scrollDirection: Axis.horizontal,
          child: tableContent,
        ),
      );
    } else {
      return tableContent;
    }
  }

  Widget _buildTabletTable(
    BuildContext context,
    BoxConstraints constraints,
    List<Agent> agents,
  ) {
    final ScrollController _horizontalScrollController = ScrollController();

    return Scrollbar(
      controller: _horizontalScrollController,
      thumbVisibility: true, // Always show scrollbar
      child: SingleChildScrollView(
        controller: _horizontalScrollController,
        scrollDirection: Axis.horizontal,
        child: SizedBox(
          width: math.max(constraints.maxWidth - (defaultPadding * 2), 800),
          child: DataTable(
            columnSpacing: defaultPadding * 0.6,
            dataRowMinHeight: 48,
            dataRowMaxHeight: 52,
            horizontalMargin: 0,
            checkboxHorizontalMargin: 0,
            columns: [
              _dataColumn(name: agentName, fontSize: 12),
              _dataColumn(name: agentContact, fontSize: 12),
              _dataColumn(name: agentEmail, fontSize: 12),
              _dataColumn(name: agentState, fontSize: 12),
              _dataColumn(name: agentLevel, fontSize: 12),
              _dataColumn(name: agentEarning, fontSize: 12),
              _dataColumn(name: agentStatus, fontSize: 12),
              _dataColumn(name: actions, fontSize: 12, allowSort: false),
            ],
            rows: agents
                .map((agent) => _agentTabletDataRow(context, agent))
                .toList(),
          ),
        ),
      ),
    );
  }

  Widget _buildMobileTable(BuildContext context, List<Agent> agents) {
    return Column(
      children: agents
          .map((agent) => _buildMobileCard(context, agent))
          .toList(),
    );
  }

  Widget _buildMobileCard(BuildContext context, Agent agent) {
    return Container(
      margin: const EdgeInsets.only(bottom: defaultPadding),
      padding: const EdgeInsets.all(defaultPadding),
      decoration: BoxDecoration(
        color: AppTheme.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _AgentNameCell(name: agent.name, isMobile: true),
          const SizedBox(height: 12),
          _buildMobileCardRow(agentContact, agent.contact),
          _buildMobileCardRow(agentEmail, agent.email),
          _buildMobileCardRow(agentState, agent.state),
          _buildMobileCardRow(agentCity, agent.city),
          _buildMobileCardRow(agentLevel, agent.level),
          _buildMobileCardRow(agentTotalDeals, agent.totalDeals.toString()),
          _buildMobileCardRow(
            agentEarning,
            '\$${agent.earning.toStringAsFixed(2)}',
          ),
          const SizedBox(height: 12),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              _buildStatusChip(agent.status),
              // _ActionButton(onPressed: () {}, isMobile: true),
              ActionButtonEye(
                onPressed: () {},
                isCompact: true,
                isMobile: true,
              ),
            ],
          ),
        ],
      ),
    );
  }

  DataColumn _dataColumn({
    String name = '',
    bool allowSort = true,
    double fontSize = 14,
  }) {
    return DataColumn(
      label: Expanded(
        child: Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Flexible(
              child: Text(
                name,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
                textAlign: TextAlign.center,
                style: AppFonts.regularTextStyle(
                  fontSize,
                  color: AppTheme.tableHeaderFont,
                ),
              ),
            ),
            if (allowSort && name != actions) ...[
              const SizedBox(width: 4),
              Image.asset(
                '$iconAssetpath/column_sort.png',
                height: 16,
                width: 16,
                color: AppTheme.tableHeaderFont,
              ),
            ],
          ],
        ),
      ),
    );
  }

  DataRow _agentDesktopDataRow(BuildContext context, Agent agent) {
    return DataRow(
      cells: [
        DataCell(_AgentNameCell(name: agent.name)),
        DataCell(
          Text(
            agent.contact,
            overflow: TextOverflow.ellipsis,
            style: AppFonts.regularTextStyle(14, color: AppTheme.tableDataFont),
          ),
        ),
        DataCell(
          Text(
            agent.email,
            overflow: TextOverflow.ellipsis,
            style: AppFonts.regularTextStyle(14, color: AppTheme.tableDataFont),
          ),
        ),
        DataCell(
          Text(
            _formatDate(agent.joinDate),
            overflow: TextOverflow.ellipsis,
            style: AppFonts.regularTextStyle(14, color: AppTheme.tableDataFont),
          ),
        ),
        DataCell(
          Text(
            agent.state,
            overflow: TextOverflow.ellipsis,
            style: AppFonts.regularTextStyle(14, color: AppTheme.tableDataFont),
          ),
        ),
        DataCell(
          Text(
            agent.city,
            overflow: TextOverflow.ellipsis,
            style: AppFonts.regularTextStyle(14, color: AppTheme.tableDataFont),
          ),
        ),
        DataCell(
          Text(
            agent.level,
            overflow: TextOverflow.ellipsis,
            style: AppFonts.regularTextStyle(14, color: AppTheme.tableDataFont),
          ),
        ),
        DataCell(
          Text(
            agent.totalDeals.toString(),
            overflow: TextOverflow.ellipsis,
            style: AppFonts.regularTextStyle(14, color: AppTheme.tableDataFont),
          ),
        ),
        DataCell(
          Text(
            '\$${agent.earning.toStringAsFixed(2)}',
            overflow: TextOverflow.ellipsis,
            style: AppFonts.regularTextStyle(14, color: AppTheme.tableDataFont),
          ),
        ),
        DataCell(_buildStatusChip(agent.status)),
        DataCell(
          ActionButtonEye(onPressed: () {}, isCompact: false, isMobile: false),
        ),
      ],
    );
  }

  DataRow _agentTabletDataRow(BuildContext context, Agent agent) {
    return DataRow(
      cells: [
        DataCell(_AgentNameCell(name: agent.name, isCompact: true)),
        DataCell(
          Text(
            agent.contact,
            overflow: TextOverflow.ellipsis,
            style: AppFonts.regularTextStyle(12, color: AppTheme.tableDataFont),
          ),
        ),
        DataCell(
          Text(
            agent.email,
            overflow: TextOverflow.ellipsis,
            style: AppFonts.regularTextStyle(12, color: AppTheme.tableDataFont),
          ),
        ),
        DataCell(
          Text(
            agent.state,
            overflow: TextOverflow.ellipsis,
            style: AppFonts.regularTextStyle(12, color: AppTheme.tableDataFont),
          ),
        ),
        DataCell(
          Text(
            agent.level,
            overflow: TextOverflow.ellipsis,
            style: AppFonts.regularTextStyle(12, color: AppTheme.tableDataFont),
          ),
        ),
        DataCell(
          Text(
            '\$${agent.earning.toStringAsFixed(0)}',
            overflow: TextOverflow.ellipsis,
            style: AppFonts.regularTextStyle(12, color: AppTheme.tableDataFont),
          ),
        ),
        DataCell(_buildStatusChip(agent.status, isCompact: true)),
        DataCell(
          ActionButtonEye(onPressed: () {}, isCompact: true, isMobile: false),
        ),
      ],
    );
  }

  Widget _buildStatusChip(bool isActive, {bool isCompact = false}) {
    return Container(
      width: 96,
      height: 29,
      padding: const EdgeInsets.fromLTRB(12, 4, 12, 4),
      decoration: BoxDecoration(
        color: isActive ? AppTheme.statusActiveBg : AppTheme.statusInactiveBg,
        borderRadius: BorderRadius.circular(25),
      ),
      child: Text(
        isActive ? active : inactive,
        textAlign: TextAlign.center,
        style: AppFonts.regularTextStyle(
          12,
          color: isActive
              ? AppTheme.statusActiveText
              : AppTheme.statusInactiveText,
        ),
      ),
    );
  }

  Widget _buildMobileCardRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label, style: AppFonts.regularTextStyle(12, color: Colors.grey)),
          Flexible(
            child: Text(
              value,
              style: AppFonts.regularTextStyle(
                12,
                color: AppTheme.tableDataFont,
              ),
              textAlign: TextAlign.right,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFooter(
    BuildContext context,
    ValueNotifier<int> currentPage,
    ValueNotifier<int> itemsPerPage,
    List<Agent> filteredAgents,
    int totalPages,
  ) {
    if (Responsive.isMobile(context)) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _showingDataText(
            context,
            currentPage.value,
            itemsPerPage.value,
            filteredAgents,
          ),
          const SizedBox(height: defaultPadding),
          _buildPagination(context, currentPage, itemsPerPage, totalPages),
        ],
      );
    }

    return Row(
      children: [
        _showingDataText(
          context,
          currentPage.value,
          itemsPerPage.value,
          filteredAgents,
        ),
        const Spacer(),
        _buildPagination(context, currentPage, itemsPerPage, totalPages),
      ],
    );
  }

  Widget _showingDataText(
    BuildContext context,
    int currentPage,
    int itemsPerPage,
    List<Agent> filteredAgents,
  ) {
    final startIndex = (currentPage - 1) * itemsPerPage + 1;
    final endIndex = (currentPage * itemsPerPage).clamp(
      0,
      filteredAgents.length,
    );

    return Text(
      '$showingDataLabelP1 $startIndex $toLabel $endIndex $ofLabel ${filteredAgents.length} entries',
      style: Theme.of(context).textTheme.bodySmall,
    );
  }

  Widget _buildPagination(
    BuildContext context,
    ValueNotifier<int> currentPage,
    ValueNotifier<int> itemsPerPage,
    int totalPages,
  ) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        _paginationButton(
          icon: Icons.chevron_left,
          onPressed: currentPage.value > 1 ? () => currentPage.value-- : null,
        ),
        ...List.generate(math.min(5, totalPages), (index) {
          final pageNum = index + 1;
          return _paginationButton(
            label: pageNum.toString(),
            isSelected: pageNum == currentPage.value,
            onPressed: () => currentPage.value = pageNum,
          );
        }),
        if (totalPages > 5) ...[
          _paginationButton(label: '...'),
          _paginationButton(
            label: totalPages.toString(),
            onPressed: () => currentPage.value = totalPages,
          ),
        ],
        _paginationButton(
          icon: Icons.chevron_right,
          onPressed: currentPage.value < totalPages
              ? () => currentPage.value++
              : null,
        ),
      ],
    );
  }

  Widget _paginationButton({
    String? label,
    IconData? icon,
    bool isSelected = false,
    VoidCallback? onPressed,
  }) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 2),
      child: Material(
        color: isSelected ? AppTheme.paginationActiveBg : Colors.transparent,
        borderRadius: BorderRadius.circular(5),
        child: InkWell(
          onTap: onPressed,
          borderRadius: BorderRadius.circular(5),
          child: Container(
            width: 30,
            height: 30,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(5),
              border: Border.all(color: Colors.grey.shade300),
            ),
            child: Center(
              child: icon != null
                  ? Icon(
                      icon,
                      size: 16,
                      color: isSelected ? Colors.white : Colors.black,
                    )
                  : Text(
                      label!,
                      style: AppFonts.regularTextStyle(
                        12,
                        color: isSelected ? Colors.white : Colors.black,
                      ),
                    ),
            ),
          ),
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.month.toString().padLeft(2, '0')}/${date.day.toString().padLeft(2, '0')}/${date.year}';
  }

  // void _navigateToAgentsPage(BuildContext context) {
  //   Navigator.pushNamed(context, '/agents');
  // }
}

class _AgentNameCell extends StatelessWidget {
  final String name;
  final bool isCompact;
  final bool isMobile;

  const _AgentNameCell({
    required this.name,
    this.isCompact = false,
    this.isMobile = false,
  });

  @override
  Widget build(BuildContext context) {
    if (isMobile) {
      return Row(
        children: [
          Image.asset(
            '$iconAssetpath/agent_round.png',
            height: isCompact ? 20 : 24,
            width: isCompact ? 20 : 24,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              name,
              style: AppFonts.boldTextStyle(14, color: AppTheme.tableDataFont),
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      );
    }

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Image.asset(
          '$iconAssetpath/agent_round.png',
          height: isCompact ? 20 : 24,
          width: isCompact ? 20 : 24,
        ),
        SizedBox(width: isCompact ? 4 : 8),
        Flexible(
          child: Text(
            name,
            overflow: TextOverflow.ellipsis,
            style: AppFonts.mediumTextStyle(
              isCompact ? 12 : 14,
              color: AppTheme.tableDataFont,
            ),
          ),
        ),
      ],
    );
  }
}
