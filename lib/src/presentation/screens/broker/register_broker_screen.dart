import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import '../../shared/components/app_textfield.dart';
import '../../../core/utils/validators.dart';
import '../../shared/components/breadcrumb_navigation.dart';
import '../dashboard/components/header.dart';
import '../../../core/utils/dotted_line_painter.dart';
import '../../shared/components/elevated_button.dart';
import '../../../core/config/app_strings.dart' as AppStrings;
import '../../../core/config/constants.dart';
import '../../../core/config/responsive.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/theme/app_fonts.dart';
import '../dashboard/components/dashboard_content.dart';

class RegisterBrokerScreen extends HookWidget {
  RegisterBrokerScreen({super.key});

  final TextEditingController firstNameController = TextEditingController();
  final TextEditingController lastNameController = TextEditingController();
  final TextEditingController phoneController = TextEditingController();
  final TextEditingController emailController = TextEditingController();
  final TextEditingController companyController = TextEditingController();
  final TextEditingController cityController = TextEditingController();
  final TextEditingController stateController = TextEditingController();
  final TextEditingController postalCodeController = TextEditingController();
  final TextEditingController countryController = TextEditingController();
  final selectedIndex = ValueNotifier(0);
  final _formKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final isMobile = Responsive.isMobile(context);
    final header = Header(selectedTab: '');

    return Scaffold(
      drawer: header.mobileDrawer,
      body: SafeArea(
        child: Column(
          children: [
            SizedBox(height: defaultPadding),
            // Static header (remains visible when scrolling)
            Padding(
              padding: EdgeInsets.symmetric(horizontal: webLayoutmargin),
              child: header,
            ),
            SizedBox(height: defaultPadding / 2),

            // The rest of the content is scrollable
            Expanded(
              child: CustomScrollView(
                slivers: [
                  SliverFillRemaining(
                    hasScrollBody: false,
                    child: _formWidget(context, size, isMobile),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _formWidget(BuildContext context, Size size, bool isMobile) {
    return ValueListenableBuilder(
      valueListenable: selectedIndex,
      builder: (context, value, child) {
        return Container(
          width: double.infinity,
          decoration: BoxDecoration(
            image: DecorationImage(
              fit: BoxFit.cover,
              image: AssetImage('$imageAssetpath/register_bg.png'),
            ),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              BreadCrumbNavigation(
                hierarchyPath: [
                  AppStrings.dashboardAdmin,
                  AppStrings.addNewBroker,
                ],
                onNavigate: (int navigationIndex) {},
              ),
              SizedBox(height: defaultPadding),
              Container(
                decoration: BoxDecoration(
                  color: AppTheme.roundIconColor,
                  borderRadius: BorderRadius.circular(25),
                ),
                margin: EdgeInsets.symmetric(
                  horizontal: isMobile
                      ? defaultPadding * 2.5
                      : selectedIndex.value == 0
                      ? size.width / 7
                      : size.width < desktopBreakpoint
                      ? size.width / 5
                      : size.width / 3,
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Header with title and buttons
                    _formHeader(context),
                    SizedBox(height: defaultPadding / 2),
                    // Main form content
                    _formContent(isMobile, context),
                  ],
                ),
              ),
              SizedBox(
                height: selectedIndex.value == 0 ? defaultPadding * 2 : 0,
              ),
              Spacer(),

              const Footer(),
            ],
          ),
        );
      },
    );
  }

  Container _formHeader(BuildContext context) {
    return Container(
      // width: double.infinity,
      padding: EdgeInsets.symmetric(
        horizontal: defaultPadding * 2,
        vertical: defaultPadding,
      ),
      decoration: BoxDecoration(borderRadius: BorderRadius.circular(25)),
      child: Center(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            const SizedBox(height: defaultPadding),
            Text(
              AppStrings.registerBroker,
              style: AppFonts.semiBoldTextStyle(18, color: Colors.white),
            ),
            const SizedBox(height: defaultPadding * 1.5),
            Container(
              width: 310,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(50),
                color: Colors.white,
              ),
              padding: const EdgeInsets.all(defaultPadding / 2),
              alignment: Alignment.center,
              child: Row(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  _registerTab(
                    context,
                    AppStrings.register,
                    selectedIndex.value == 0,
                    0,
                  ),

                  const SizedBox(width: defaultPadding),
                  _registerTab(
                    context,
                    AppStrings.inviteBroker,
                    selectedIndex.value == 1,
                    1,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _registerTab(
    BuildContext context,
    String label,
    bool isActive,
    int index,
  ) {
    return AppButton(
      label: label,
      backgroundColor: isActive ? AppTheme.roundIconColor : Colors.white,
      foregroundColor: Colors.white,
      elevation: 0,
      borderSide: const BorderSide(color: Colors.white),
      textStyle: AppFonts.mediumTextStyle(
        14,
        color: isActive ? Colors.white : AppTheme.roundIconColor,
      ),
      onPressed: () {
        _clearForm();
        selectedIndex.value = index;
      },
    );
  }

  Container _formContent(bool isMobile, BuildContext context) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(isMobile ? defaultPadding : defaultPadding * 2),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(25),
      ),
      child: Form(
        key: _formKey,
        child: Column(
          children: [
            Responsive(
              mobile: _buildMobileLayout(context),
              desktop: _buildDesktopLayout(context),
            ),
            SizedBox(height: defaultPadding * 2),
            _actionButtons(context),
          ],
        ),
      ),
    );
  }

  Widget _buildMobileLayout(BuildContext context) {
    bool isRegisterTab = selectedIndex.value == 0;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        if (isRegisterTab) ...[
          // Broker Information Section
          _fieldHead(AppStrings.brokerInformation, 18),
          const SizedBox(height: defaultPadding * 2),
        ],
        // First Name
        _buildFormLabel(AppStrings.firstName, isMandatory: true),
        const SizedBox(height: 8),
        _buildTextFormField(
          firstNameController,
          AppStrings.enterFirstName,
          isMandatory: true,
        ),
        const SizedBox(height: defaultPadding),

        // Last Name
        _buildFormLabel(AppStrings.lastName, isMandatory: true),
        const SizedBox(height: 8),
        _buildTextFormField(
          lastNameController,
          AppStrings.enterLastName,
          isMandatory: true,
        ),
        const SizedBox(height: defaultPadding),

        // Phone
        _buildFormLabel(AppStrings.phone, isMandatory: true),
        const SizedBox(height: 8),
        _phoneNumTextFormField(),
        const SizedBox(height: defaultPadding),

        // Email
        _buildFormLabel(AppStrings.email, isMandatory: true),
        const SizedBox(height: 8),
        _emailTextFormField(),
        const SizedBox(height: defaultPadding),
        if (isRegisterTab) ...[
          // Company
          _buildFormLabel(AppStrings.company, isMandatory: true),
          const SizedBox(height: 8),
          _buildTextFormField(
            companyController,
            AppStrings.enterCompany,
            isMandatory: true,
          ),
          const SizedBox(height: defaultPadding),

          // City
          _buildFormLabel(AppStrings.city),
          const SizedBox(height: 8),
          _buildTextFormField(cityController, ''),
          const SizedBox(height: defaultPadding),

          // State/Province
          _buildFormLabel(AppStrings.stateProvince),
          const SizedBox(height: 8),
          _buildTextFormField(stateController, ''),
          const SizedBox(height: defaultPadding),

          // Postal/Zip Code
          _buildFormLabel(AppStrings.postalZipCode),
          const SizedBox(height: 8),
          _buildTextFormField(postalCodeController, ''),
          const SizedBox(height: defaultPadding),

          // Country
          _buildFormLabel(AppStrings.country),
          const SizedBox(height: 8),
          _buildTextFormField(countryController, ''),
          const SizedBox(height: defaultPadding * 2),

          // Upload Documents Section
          _fieldHead(AppStrings.uploadDocuments, 18),

          const SizedBox(height: defaultPadding * 2),

          // E&O Insurance Certificate
          _buildFormLabel(AppStrings.eoInsuranceCertificate),
          const SizedBox(height: 8),
          _buildUploadField(
            AppStrings.chooseFileOrDragDrop,
            AppStrings.pdfOrImageOnly,
          ),
          const SizedBox(height: defaultPadding),

          // Brokerage License
          _buildFormLabel(AppStrings.brokerageLicense),
          const SizedBox(height: 8),
          _buildUploadField(
            AppStrings.chooseFileOrDragDrop,
            AppStrings.pdfOrImageOnly,
          ),
          const SizedBox(height: defaultPadding),

          // Principal Broker ID
          _buildFormLabel(AppStrings.principalBrokerId),
          const SizedBox(height: 8),
          _buildUploadField(
            AppStrings.chooseFileOrDragDrop,
            AppStrings.pdfOrImageOnly,
          ),
          const SizedBox(height: defaultPadding),

          // Logo
          _buildFormLabel(AppStrings.logo),
          const SizedBox(height: 8),
          _buildUploadField(
            AppStrings.chooseImageOrDragDrop,
            AppStrings.imageFormatsOnly,
          ),
        ],
        const SizedBox(height: defaultPadding * 2),
      ],
    );
  }

  Widget _emailTextFormField() {
    return _buildTextFormField(
      emailController,
      AppStrings.enterEmail,
      isMandatory: true,
      keyboardType: TextInputType.emailAddress,
      validator: (value) => InputValidators.validateEmail(value),
    );
  }

  Widget _phoneNumTextFormField() {
    return _buildTextFormField(
      phoneController,
      AppStrings.enterPhone,
      isMandatory: true,
      keyboardType: TextInputType.phone,
      inputFormatters: [FilteringTextInputFormatter.digitsOnly],
      validator: (value) => InputValidators.validatePhone(value),
    );
  }

  Widget _fieldHead(String title, double fontSize) {
    return Text(
      title,
      textAlign: TextAlign.center,
      style: AppFonts.semiBoldTextStyle(
        fontSize,
        color: AppTheme.primaryTextColor,
      ),
    );
  }

  Widget _buildDesktopLayout(BuildContext context) {
    bool isRegisterTab = selectedIndex.value == 0;
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Left Column - Broker Information
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              if (isRegisterTab) ...[
                _fieldHead(AppStrings.brokerInformation, 18),
                const SizedBox(height: defaultPadding * 2),
              ],

              // First Name and Last Name
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildFormLabel(
                          AppStrings.firstName,
                          isMandatory: true,
                        ),
                        const SizedBox(height: 8),
                        _buildTextFormField(
                          firstNameController,
                          AppStrings.enterFirstName,
                          isMandatory: true,
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(width: defaultPadding),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildFormLabel(AppStrings.lastName, isMandatory: true),
                        const SizedBox(height: 8),
                        _buildTextFormField(
                          lastNameController,
                          AppStrings.enterLastName,
                          isMandatory: true,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: defaultPadding),

              // Phone
              _buildFormLabel(AppStrings.phone, isMandatory: true),
              const SizedBox(height: 8),
              _phoneNumTextFormField(),
              const SizedBox(height: defaultPadding),
              // Email
              _buildFormLabel(AppStrings.email, isMandatory: true),
              const SizedBox(height: 8),
              _emailTextFormField(),
              const SizedBox(height: defaultPadding),

              if (isRegisterTab) ...[
                // Company
                _buildFormLabel(AppStrings.company, isMandatory: true),
                const SizedBox(height: 8),
                _buildTextFormField(
                  companyController,
                  AppStrings.enterCompany,
                  isMandatory: true,
                ),
                const SizedBox(height: defaultPadding),

                // City and State/Province
                _textFormFieldRowWidget(
                  AppStrings.city,
                  cityController,
                  AppStrings.stateProvince,
                  stateController,
                ),

                const SizedBox(height: defaultPadding),

                // Postal/Zip Code and Country
                _textFormFieldRowWidget(
                  AppStrings.postalZipCode,
                  postalCodeController,
                  AppStrings.country,
                  countryController,
                ),
              ],
            ],
          ),
        ),

        const SizedBox(width: defaultPadding * 2),
        if (isRegisterTab) ...[
          // Right Column - Upload Documents
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                _fieldHead(AppStrings.uploadDocuments, 18),
                const SizedBox(height: defaultPadding * 2),

                // E&O Insurance Certificate
                _buildFormLabel(AppStrings.eoInsuranceCertificate),
                const SizedBox(height: 8),
                _buildUploadField(
                  AppStrings.chooseFileOrDragDrop,
                  AppStrings.pdfOrImageOnly,
                ),
                const SizedBox(height: defaultPadding),

                // Brokerage License
                _buildFormLabel(AppStrings.brokerageLicense),
                const SizedBox(height: 8),
                _buildUploadField(
                  AppStrings.chooseFileOrDragDrop,
                  AppStrings.pdfOrImageOnly,
                ),
                const SizedBox(height: defaultPadding),

                // Principal Broker ID
                _buildFormLabel(AppStrings.principalBrokerId),
                const SizedBox(height: 8),
                _buildUploadField(
                  AppStrings.chooseFileOrDragDrop,
                  AppStrings.pdfOrImageOnly,
                ),
                const SizedBox(height: defaultPadding),

                // Logo
                _buildFormLabel(AppStrings.logo),
                const SizedBox(height: 8),
                _buildUploadField(
                  AppStrings.chooseImageOrDragDrop,
                  AppStrings.imageFormatsOnly,
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }

  Widget _textFormFieldRowWidget(
    String leftLabel,
    TextEditingController leftController,
    String rightLabel,
    TextEditingController rightController,
  ) {
    return Row(
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildFormLabel(leftLabel),
              const SizedBox(height: 8),
              _buildTextFormField(leftController, ''),
            ],
          ),
        ),
        const SizedBox(width: defaultPadding),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildFormLabel(rightLabel),
              const SizedBox(height: 8),
              _buildTextFormField(rightController, ''),
            ],
          ),
        ),
      ],
    );
  }

  Widget _actionButtons(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        AppButton(
          label: AppStrings.clear,
          backgroundColor: AppTheme.scaffoldBgColor,
          foregroundColor: AppTheme.primaryTextColor,
          borderRadius: 25,
          padding: const EdgeInsets.symmetric(
            horizontal: defaultPadding * 2.5,
            vertical: defaultPadding / 2,
          ),
          onPressed: () => _showClearDataAlert(context),
        ),

        const SizedBox(width: defaultPadding),
        AppButton(
          label: AppStrings.register,
          backgroundColor: AppTheme.roundIconColor,
          foregroundColor: Colors.white,
          borderRadius: 25,
          padding: const EdgeInsets.symmetric(
            horizontal: defaultPadding * 2,
            vertical: defaultPadding / 2,
          ),
          onPressed: () => _submitForm(context),
        ),
      ],
    );
  }

  Widget _buildFormLabel(String label, {bool isMandatory = false}) {
    return Align(
      alignment: Alignment.centerLeft,
      child: RichText(
        text: TextSpan(
          text: label,
          style: AppFonts.regularTextStyle(
            14,
            color: AppTheme.primaryTextColor,
          ),
          children: isMandatory
              ? [
                  TextSpan(
                    text: ' *',
                    style: AppFonts.regularTextStyle(
                      14,
                      color: AppTheme.textFieldMandatoryColor,
                    ),
                  ),
                ]
              : [],
        ),
      ),
    );
  }

  Widget _buildTextFormField(
    TextEditingController controller,
    String hintText, {
    bool isMandatory = false,
    String? Function(String?)? validator,
    TextInputType? keyboardType,
    List<TextInputFormatter>? inputFormatters,
  }) {
    return AppTextField(
      controller: controller,
      hintText: hintText,
      isMandatory: isMandatory,
      validator: validator,
      keyboardType: keyboardType,
      inputFormatters: inputFormatters,
      isMobile: false,
    );
  }

  Widget _buildUploadField(String hintText, String formatText) {
    return buildDottedBorderContainerWithRadius(
      borderRadius: 25.0,
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(defaultPadding),
        decoration: BoxDecoration(
          color: AppTheme.docUploadBgColor,
          borderRadius: BorderRadius.circular(25),
        ),
        child: Column(
          children: [
            ElevatedButton.icon(
              onPressed: () {},
              icon: Image.asset(
                '$iconAssetpath/upload.png',
                height: 16,
                width: 16,
              ),
              label: Text(
                AppStrings.upload,
                style: AppFonts.mediumTextStyle(14, color: AppTheme.black),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.white,
                foregroundColor: AppTheme.primaryTextColor,
                elevation: 0,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                  side: BorderSide(color: AppTheme.borderColor),
                ),
              ),
            ),
            const SizedBox(height: 8),
            Text(
              hintText,
              textAlign: TextAlign.center,
              style: AppFonts.mediumTextStyle(12, color: AppTheme.black),
            ),
            Text(
              formatText,
              textAlign: TextAlign.center,
              style: AppFonts.regularTextStyle(
                12,
                color: AppTheme.ternaryTextColor,
              ),
            ),
          ],
        ),
      ),
    );
  }

  _clearForm() {
    firstNameController.clear();
    lastNameController.clear();
    phoneController.clear();
    emailController.clear();
    companyController.clear();
    cityController.clear();
    stateController.clear();
    postalCodeController.clear();
    countryController.clear();
  }

  _submitForm(BuildContext context) {
    if (_formKey.currentState!.validate()) {
      // Form is valid, proceed with submission
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text(AppStrings.processingData)));
      // TODO: Add your submission logic here
    } else {
      // Form is invalid, show error message
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text(AppStrings.pleaseFillRequiredFields)),
      );
    }
  }

  _showClearDataAlert(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Text(AppStrings.clearData),
          content: Text(
            AppStrings.clearDataConfirmation,
            textAlign: TextAlign.center,
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: Text(AppStrings.cancel),
            ),
            TextButton(
              onPressed: () {
                _clearForm();
                Navigator.pop(context);
              },
              child: Text(AppStrings.ok),
            ),
          ],
        );
      },
    );
  }
}
