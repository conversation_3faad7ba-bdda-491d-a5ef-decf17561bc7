import 'package:flutter/material.dart';
import 'package:neorevv/src/core/config/constants.dart';
import 'package:neorevv/src/core/theme/app_fonts.dart';
import 'package:neorevv/src/core/theme/app_theme.dart';
import 'package:neorevv/src/domain/models/report.dart';

class ReportSidebar extends StatelessWidget {
  final List<ReportModel> reports;
  final Function(ReportModel) onReportSelected;

  const ReportSidebar({
    super.key,
    required this.reports,
    required this.onReportSelected,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: ResponsiveSizes.reportSidebarWidth(context),
      height: ResponsiveSizes.reportSidebarHeight(context),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Report List
          Expanded(
            child: ListView.builder(
              // padding: ResponsiveSizes.reportItemContentPadding(context),
              itemCount: reports.length,
              itemBuilder: (context, index) {
                final report = reports[index];
                return _buildReportItem(context, report);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildReportItem(BuildContext context, ReportModel report) {
    return Container(
      height: 32,
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: report.isSelected ? AppTheme.searchbarBg : Colors.transparent,
        borderRadius: BorderRadius.circular(5),
        border: Border.all(color: AppTheme.searchbarBg, width: 1),
      ),
      child: InkWell(
        onTap: () => onReportSelected(report),
        borderRadius: BorderRadius.circular(5),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
          child: Row(
            children: [
              Image.asset(
                '$iconAssetpath/fi-rs-copy-alt.png',
                width: 12,
                height: 12,
                color: AppTheme.black,
              ),
              const SizedBox(width: 6),
              Expanded(
                child: Text(
                  report.title,
                  style: AppFonts.mediumTextStyle(12, color: AppTheme.black),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
